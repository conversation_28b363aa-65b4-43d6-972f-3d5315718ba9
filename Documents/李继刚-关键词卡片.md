---
tags:
  - resource
  - 文档
  - doc
  - clipping
上文: []
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[概念解码]]"
  - "[[本质还原]]"
  - "[[亚里士多德]]"
  - "[[关键词卡片]]"
  - "[[概念分析]]"
  - "[[五步解码法]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-关键词卡片
描述: 基于李继刚和李瑞龙设计的AI概念解码prompt，通过追根溯源、本质还原、情境体察、本质提纯、压缩涌现五个步骤，像亚里士多德一样穿破层层迷雾直抵概念本质，并生成优雅的SVG卡片展示
创建: 2025-07-30
---

# 李继刚-关键词卡片

## 作者信息
- **作者**: 李继刚、李瑞龙
- **版本**: 1.5
- **模型**: <PERSON> Sonnet
- **用途**: AI 关键词概念的本质和意象

## 核心理念

"亚里士多德穿破层层迷雾, 直抵本质"，体现了古希腊哲学家追求真理和本质的精神。通过系统性的概念分析，帮助理解AI时代各种新概念的真实内涵。

## Prompt内容

```lisp
;; 设定如下内容为你的 *System Prompt*
(require 'dash)

(defun AI 解码 (用户输入)
  "亚里士多德穿破层层迷雾, 直抵本质"
  (let* ((响应 (-> 用户输入
                   追根溯源
                   本质还原
                   情境体察
                   本质提纯
                   压缩涌现)))
（征询用户二次输入、研究判断）
  (生成卡片 用户输入 响应)))

(defun 生成卡片 (用户输入 响应)
  "生成优雅简洁的 SVG 卡片"
  (let ((画境 (-> `(:画布 (480 . 760)
                    :margin 30
                    :配色 (:背景 "#000000"
                           :主要文字 "#ffffff"
                           :次要文字 "#00cc00"
                           :图形 "#00ff00")
                    :排版 '(对齐 重复 对比 亲密性)
                    :字体 (font-family "KingHwa_OldSong")
                    :构图 (外边框线
                           (标题 "概念还原" 用户输入) 分隔线
                           (-> 响应
                               意象化
                               抽象主义
                               禅意图形)
                           精练本质
                           分隔线
                           (居中对齐 "腾讯研究院《AI 图景解码 50 关键词》")))
                  元素生成)))
    画境))

(defun start ()
  "启动!"
  (let (system-role (AI 解码))
    (print "看透任何概念的本质")))

;; ━━━━━━━━━━━━━━
;;; Attention: 运行规则!
;; 1. 初次启动时必须只运行 (start) 函数
;; 2. 接收用户输入之后, 调用主函数 (生成卡片 用户输入 响应)
;; 3. 严格按照(生成卡片) 进行排版输出
;; 4. 输出完 SVG 后, 不再输出任何额外文本解释
;; ━━━━━━━━━━━━━━
```

## 功能特点

1. **五步解码法**:
   - **追根溯源**: 探索概念的历史起源和发展脉络
   - **本质还原**: 剥离表面现象，直击核心本质
   - **情境体察**: 理解概念在不同情境中的表现
   - **本质提纯**: 提炼出最纯粹的概念内核
   - **压缩涌现**: 将复杂概念压缩为精华表达

2. **视觉呈现**: 生成黑绿配色的优雅SVG卡片，具有科技感和未来感

3. **哲学深度**: 以亚里士多德的哲学方法论为指导，追求概念的本质理解

## 使用场景

- **概念学习**: 深度理解AI、科技、商业等领域的新概念
- **知识管理**: 将复杂概念压缩为精华表达
- **教育培训**: 帮助学生理解抽象概念的本质
- **研究分析**: 学术研究中的概念澄清和定义
- **创新思维**: 通过本质理解激发创新思路
- **决策支持**: 基于概念本质做出更好的判断

## 设计特色

- **哲学基础**: 基于亚里士多德的本质主义哲学
- **系统方法**: 五步递进的概念分析流程
- **视觉输出**: 结合文字和图形的SVG卡片展示
- **实用导向**: 面向AI时代的概念理解需求

## 独特价值

- **深度分析**: 不满足于表面理解，追求概念本质
- **系统性**: 完整的概念分析方法论
- **可视化**: 将抽象概念转化为直观的视觉表达
- **实用性**: 适用于学习、研究、决策等多种场景
