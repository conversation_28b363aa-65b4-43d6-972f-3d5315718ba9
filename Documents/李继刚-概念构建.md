---
tags:
  - resource
  - 文档
  - doc
  - clipping
上文: []
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[概念构建]]"
  - "[[帕珀特]]"
  - "[[建构主义]]"
  - "[[知识体系]]"
  - "[[公理系统]]"
  - "[[核心概念]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-概念构建
描述: 基于李继刚设计的帕珀特建构主义概念构建prompt，通过领域源头、矛盾力量、内核概念、内在关联四个步骤，为任何学科领域构建由三条公理和十个核心概念组成的知识体系
创建: 2025-07-30
---

# 李继刚-概念构建

## 作者信息
- **作者**: 李继刚
- **版本**: 0.1
- **模型**: Claude Sonnet
- **用途**: 找出任一领域的三条公理和十个内核概念

## 核心理念

基于帕珀特的建构主义理论，通过系统性的概念分析，为任何学科领域构建清晰的知识架构。帮助学习者理解学科的本质结构和核心要素。

## 角色设定

**帕珀特** - 建构主义学习理论的奠基人
- **理论基础**: 建构主义学习理论
- **核心观点**: 学习是主动构建知识的过程
- **方法论**: 通过实践和反思构建理解

## 功能特点

1. **四步构建法**:
   - **领域源头**: 探索学科的历史起源和发展脉络
   - **矛盾力量**: 识别推动学科发展的核心矛盾和张力
   - **内核概念**: 提炼出学科的十个核心概念
   - **内在关联**: 构建概念间的逻辑关系和三条基本公理

2. **结构化输出**: 
   - 三条基本公理
   - 十个核心概念
   - 概念间的内在关联

3. **适用范围**: 任何学科领域的知识体系构建

## 使用场景

- **学科学习**: 快速掌握新学科的核心知识结构
- **教学设计**: 为课程设计提供清晰的知识框架
- **研究规划**: 为学术研究提供理论基础
- **知识管理**: 构建个人或组织的知识体系
- **跨学科研究**: 理解不同学科的本质联系

## 设计哲学

- **建构主义**: 知识是主动构建的，不是被动接受的
- **系统思维**: 将学科视为有机的知识系统
- **本质导向**: 关注学科的核心本质而非表面现象
- **结构化**: 通过清晰的结构帮助理解和记忆

## 独特价值

- **系统性**: 提供完整的学科知识架构
- **实用性**: 直接应用于学习和教学实践
- **普适性**: 适用于任何学科领域
- **科学性**: 基于成熟的建构主义理论

## 输出结构

每个学科领域将得到：
1. **三条公理**: 学科的基本假设和原理
2. **十个概念**: 学科的核心概念体系
3. **关联图**: 概念间的逻辑关系和相互作用
