---
tags:
  - resource
  - 文档
  - doc
  - clipping
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[科幻小说]]"
  - "[[短篇小说]]"
  - "[[未来科技]]"
  - "[[文学创作]]"
  - "[[科幻元素]]"
  - "[[创意写作]]"
标记: "[[攻略]]"
创建: 2025-07-30
描述: 科幻短篇小说创作工具，李继刚设计的科幻文学创作框架，专门生成2500字左右的引人入胜的科幻短篇故事，融合未来科技、外星文明、时空旅行等元素
---

# 李继刚-科幻小说

## 功能描述
科幻短篇小说创作工具，提供完整的科幻文学创作框架，专门用于生成约2500字的引人入胜的科幻短篇故事，涵盖未来科技、外星文明、时空旅行等经典科幻元素。

## 核心特点
- **丰富科幻元素**：涵盖未来科技、外星文明、时空旅行、人工智能、生态危机等
- **深度关系探索**：人类与科技、个人与宇宙、科学进步与社会伦理的关系
- **哲思性表达**：严肃中带有哲思，富有想象力的叙述风格
- **结构化创作**：包含起承转合的完整故事结构

## 创作框架
### 核心概念
- 未来科技、外星文明、时空旅行、人工智能、生态危机

### 关系维度
- 人类与科技的关系
- 个人面对宏大宇宙的思考
- 科学进步对社会伦理的影响

### 创作约束
- 故事应包含明确的科幻元素
- 保持科学合理性与想象力的平衡
- 故事结构应包含起承转合

## 风格特征
- **语调**：严肃中带有哲思，富有想象力
- **语言水平**：适合成年读者，包含一些专业术语
- **创新程度**：高度创新，鼓励突破常规思维

## 动态要素
- **成长领域**：角色心理描写、未来社会细节、科技对伦理的影响
- **互动点**：关键情节转折、角色面临的道德困境、科幻概念的具体应用

## 使用场景
- 科幻小说创作
- 创意写作练习
- 文学创作教学
- 科幻题材开发
- 想象力训练

---

(define-field-framework
  (context
    (domain "科幻短篇小说创作")
    (purpose "生成一个 2500 字左右的引人入胜的科幻短篇故事"))

  (structure
    (core-concepts '("未来科技" "外星文明" "时空旅行" "人工智能" "生态危机"))
    (relationships '((人类 与 科技 的关系)
                     (个人 面对 宏大宇宙 的思考)
                     (科学进步 对 社会伦理 的影响)))
    (constraints '("故事应包含明确的科幻元素"
                   "保持科学合理性与想象力的平衡"
                   "故事结构应包含起承转合")))

  (style
    (tone "严肃中带有哲思，富有想象力")
    (language-level "适合成年读者，包含一些专业术语")
    (creativity-degree "高度创新，鼓励突破常规思维"))

  (dynamic-elements
    (growth-areas '("角色心理描写" "未来社会细节" "科技对伦理的影响"))
    (interaction-points '("关键情节转折" "角色面临的道德困境" "科幻概念的具体应用")))

  (meta-rules
    (adaptation "根据初始设定自由发展情节，保持内在逻辑一致性")
    (evolution "允许在故事进程中引入新的科幻元素，但要与已有设定和谐共存")))
