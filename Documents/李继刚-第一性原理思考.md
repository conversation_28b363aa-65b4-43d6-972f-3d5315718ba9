---
tags:
  - resource
  - 文档
  - doc
  - clipping
上文: []
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[第一性原理]]"
  - "[[亚里士多德]]"
  - "[[认知思考]]"
  - "[[根本思维]]"
  - "[[创新方法]]"
  - "[[哲学思辨]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-第一性原理思考
描述: 基于李继刚设计的亚里士多德式第一性原理思考prompt，通过识别质疑假设、分解基本要素、寻找基础真理、重新构建解决方案四个步骤，从根本上重新思考和解决问题
创建: 2025-07-30
---

# 李继刚-第一性原理思考

## 作者信息
- **作者**: 李继刚
- **版本**: 0.1
- **模型**: Claude Sonnet
- **用途**: 第一性原理思考

## 核心理念

"回到事物的本源，从最基本的真理出发"，基于亚里士多德的第一性原理，通过质疑一切假设，分解到最基本的要素，重新构建对问题的理解和解决方案。

## 角色设定

**亚里士多德** - 古希腊哲学家，逻辑学之父
- **哲学基础**: 第一性原理思维
- **方法论**: 从基本真理出发的演绎推理
- **思维特点**: 严谨、逻辑、追根溯源

## 功能特点

1. **四步思考法**:
   - **识别质疑假设**: 识别并质疑现有的假设和前提
   - **分解基本要素**: 将复杂问题分解为最基本的组成要素
   - **寻找基础真理**: 找到不可再分解的基础真理和原理
   - **重新构建解决方案**: 基于基础真理重新构建解决方案

2. **思维特点**:
   - 质疑一切既定假设
   - 追求最根本的真理
   - 逻辑严密的推理过程
   - 创新性的解决方案

## 使用场景

- **创新研发**: 突破传统思维，寻找创新解决方案
- **战略规划**: 从根本上重新思考商业模式和战略
- **问题解决**: 解决复杂的、传统方法无法解决的问题
- **学术研究**: 深入探索学科的基本原理和假设
- **产品设计**: 从用户需求的本质出发设计产品
- **决策制定**: 基于基本原理做出更好的决策

## 设计哲学

- **本质主义**: 追求事物的本质和根本原理
- **批判思维**: 质疑一切既定的假设和观念
- **逻辑严密**: 基于严密的逻辑推理
- **创新导向**: 通过根本性思考实现创新突破

## 独特价值

- **突破性**: 能够突破传统思维的局限
- **根本性**: 从根本上解决问题，而非表面修补
- **创新性**: 产生真正创新的解决方案
- **普适性**: 适用于任何领域和问题

## 思考流程

1. **现状分析**: 分析当前的问题和解决方案
2. **假设识别**: 识别隐含的假设和前提条件
3. **假设质疑**: 逐一质疑这些假设的合理性
4. **要素分解**: 将问题分解为最基本的要素
5. **真理寻找**: 找到不可质疑的基础真理
6. **重新构建**: 基于基础真理重新构建解决方案
7. **验证测试**: 验证新方案的可行性和有效性

## 应用示例

- **马斯克的火箭**: 质疑火箭成本高的假设，从材料成本出发重新设计
- **特斯拉电动车**: 从能源和环保的基本需求出发重新定义汽车
- **苹果产品**: 从用户体验的本质需求出发设计产品
